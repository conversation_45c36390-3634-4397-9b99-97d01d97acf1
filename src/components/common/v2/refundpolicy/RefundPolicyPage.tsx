"use client";

import { Box, Divider, List, rem, Stack, Text, Title } from "@mantine/core";
import { motion } from "framer-motion";
import { NextPage } from "next";
import { Footer } from "../footer/Footer";
import { Header } from "../header/Header";
import classes from "./RefundPolicyPage.module.css";

const RefundPolicyPage: NextPage = () => {
  return (
    <>
      <div className={classes.pageWrapper}>
        <Header />
      </div>
      <Box className={classes.titleSection}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Title className={classes.title}>Refund & Cancellation Policy</Title>
        </motion.div>
      </Box>
      <Box
        component="section"
        px={{ base: rem(30), md: rem(100), lg: rem(250) }}
        py={rem(60)}
      >
        <Stack
          gap={0}
          style={{
            marginTop: rem(15),
            marginLeft: rem(4),
            marginBottom: rem(30),
          }}
        >
          <Text fw={700}>Effective Date: May 30, 2025</Text>
          <Text fw={700}>Last Updated: September 3, 2025</Text>
        </Stack>
        <Stack>
          <Box>
            <Text>
              At{" "}
              <Text span className={classes.span}>
                Colecta AI Pvt Ltd
              </Text>{" "}
              ("Colecta AI," "we," "our," or "us"), we aim to deliver a seamless
              experience for creators and brands through our AI-powered
              management platform. This Refund & Cancellation Policy outlines
              the rules regarding payments, subscription cancellations, and
              refunds. By using our services, you agree to the terms below.
            </Text>
          </Box>

          <Divider my="lg" />

          <Box>
            <Title order={1} mb={rem(20)} className={classes.contentTitle}>
              1. Subscriptions and Commissions
            </Title>
            <Text my="sm">
              Colecta AI provides subscription plans (monthly or annual,
              auto-renewable) as well as commission-based services.
            </Text>
            <Text my="sm">
              All fees and charges are final and non-refundable, except as
              specifically outlined in this policy or where required under
              Indian law.
            </Text>
          </Box>

          <Divider my="lg" />

          <Box>
            <Title order={1} mb={rem(20)} className={classes.contentTitle}>
              2. Cancellation of Subscriptions
            </Title>
            <List withPadding spacing="sm">
              <List.Item>
                Users may cancel their subscription at any time from within the
                platform settings.
              </List.Item>
              <List.Item>
                Cancellation will stop the auto-renewal of the subscription.
              </List.Item>
              <List.Item>
                You will continue to have access to premium features until the
                end of the current billing cycle.
              </List.Item>
              <List.Item>
                No refunds or credits will be issued for partial usage, unused
                features, or the remaining period of the billing cycle.
              </List.Item>
            </List>
          </Box>

          <Divider my="lg" />

          <Box>
            <Title order={1} mb={rem(20)} className={classes.contentTitle}>
              3. Automatic Renewals
            </Title>
            <List withPadding spacing="sm">
              <List.Item>
                Subscriptions are automatically renewed at the end of each
                billing period unless canceled before the renewal date.
              </List.Item>
              <List.Item>
                Renewal charges are non-refundable once processed.
              </List.Item>
              <List.Item>
                Users are responsible for canceling their subscription in
                advance if they do not wish to continue.
              </List.Item>
            </List>
          </Box>

          <Divider my="lg" />

          <Box>
            <Title order={1} mb={rem(20)} className={classes.contentTitle}>
              4. Refunds for Technical Issues
            </Title>
            <Text my="sm">
              Refunds may be provided only in cases of proven technical failures
              on our platform that prevent access to paid services.
            </Text>
            <Text my="sm">
              Refund requests must be submitted to{" "}
              <Text
                component="a"
                href="mailto:<EMAIL>"
                className={classes.link}
              >
                <EMAIL>
              </Text>{" "}
              within 7 days of the issue occurring. Supporting evidence (e.g.,
              error messages, screenshots) may be required.
            </Text>
            <Text my="sm">
              If validated by our support team, refunds will be processed to the
              original payment method within 7–14 business days.
            </Text>
          </Box>

          <Divider my="lg" />

          <Box>
            <Title order={1} mb={rem(20)} className={classes.contentTitle}>
              5. No Refund Policy
            </Title>
            <Text my="sm">
              Except in cases of verified technical issues as described above,
              Colecta AI follows a strict no-refund policy. Refunds will not be
              issued for:
            </Text>
            <List withPadding spacing="sm">
              <List.Item>Subscription cancellations made mid-cycle</List.Item>
              <List.Item>
                Change of mind or dissatisfaction with features
              </List.Item>
              <List.Item>Failure to use the platform</List.Item>
              <List.Item>Commission-based fees once processed</List.Item>
            </List>
          </Box>

          <Divider my="lg" />

          <Box>
            <Title order={1} mb={rem(20)} className={classes.contentTitle}>
              6. Governing Law
            </Title>
            <Text>
              This Refund & Cancellation Policy shall be governed by and
              construed in accordance with the laws of India. Any disputes
              arising shall be subject to the exclusive jurisdiction of the
              courts in Bhopal, Madhya Pradesh, India.
            </Text>
          </Box>

          <Divider my="lg" />

          <Box>
            <Title order={1} mb={rem(20)} className={classes.contentTitle}>
              7. Contact Us
            </Title>
            <Text mb="sm">
              For cancellations, refund requests, or further inquiries, please
              reach us at:
            </Text>
            <Stack gap="xs" mt="md">
              <Text fw={600}>Colecta AI Pvt Ltd</Text>
              <Text>FLAT NO-604 DK-4 LEELA ATULYAM</Text>
              <Text>MISROD Huzur Madhya Pradesh 462039</Text>
              <Text>Trilanga, Bhopal, India</Text>
              <Text mt="sm">
                📧 Email:{" "}
                <Text
                  component="a"
                  href="mailto:<EMAIL>"
                  className={classes.link}
                >
                  <EMAIL>
                </Text>
              </Text>
            </Stack>
          </Box>
        </Stack>
      </Box>
      <Footer />
    </>
  );
};

export default RefundPolicyPage;
